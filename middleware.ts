import { NextRequest, NextResponse } from 'next/server';
import { createSecurityMiddleware, rateLimiters } from './lib/security';
import { env } from './lib/env-validation';

// Security middleware instance
const securityMiddleware = createSecurityMiddleware();

export async function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;

    // Skip middleware for static files and Next.js internals
    if (
        pathname.startsWith('/_next/') ||
        pathname.startsWith('/api/_next/') ||
        pathname.includes('.') ||
        pathname.startsWith('/favicon')
    ) {
        return NextResponse.next();
    }

    // Apply security middleware
    const securityResponse = await securityMiddleware(request);
    if (securityResponse.status !== 200) {
        return securityResponse;
    }

    // Enhanced rate limiting for specific routes
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    
    // Auth endpoints - stricter rate limiting
    if (pathname.startsWith('/api/auth/') || pathname.startsWith('/auth')) {
        const authRateLimit = await rateLimiters.auth.isAllowed(clientIP);
        if (!authRateLimit.allowed) {
            return new NextResponse('Too Many Authentication Attempts', {
                status: 429,
                headers: {
                    'Retry-After': Math.ceil((authRateLimit.resetTime - Date.now()) / 1000).toString(),
                    'X-RateLimit-Limit': '5',
                    'X-RateLimit-Remaining': '0',
                    'X-RateLimit-Reset': authRateLimit.resetTime.toString()
                }
            });
        }
    }

    // Upload endpoints - specific rate limiting
    if (pathname.startsWith('/api/upload')) {
        const uploadRateLimit = await rateLimiters.upload.isAllowed(clientIP);
        if (!uploadRateLimit.allowed) {
            return new NextResponse('Too Many Upload Requests', {
                status: 429,
                headers: {
                    'Retry-After': Math.ceil((uploadRateLimit.resetTime - Date.now()) / 1000).toString(),
                    'X-RateLimit-Limit': '10',
                    'X-RateLimit-Remaining': '0',
                    'X-RateLimit-Reset': uploadRateLimit.resetTime.toString()
                }
            });
        }
    }

    // CORS handling for API routes
    if (pathname.startsWith('/api/')) {
        const origin = request.headers.get('origin');
        const allowedOrigins = env.ALLOWED_ORIGINS?.split(',') || [env.NEXT_PUBLIC_BASE_URL];
        
        // Handle preflight requests
        if (request.method === 'OPTIONS') {
            return new NextResponse(null, {
                status: 200,
                headers: {
                    'Access-Control-Allow-Origin': allowedOrigins.includes(origin || '') ? origin || '*' : 'null',
                    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-CSRF-Token',
                    'Access-Control-Max-Age': '86400',
                    'Access-Control-Allow-Credentials': 'true'
                }
            });
        }

        // Add CORS headers to API responses
        const response = NextResponse.next();
        response.headers.set(
            'Access-Control-Allow-Origin',
            allowedOrigins.includes(origin || '') ? origin || '*' : 'null'
        );
        response.headers.set('Access-Control-Allow-Credentials', 'true');
        
        return response;
    }

    // Content Security Policy for pages
    const response = NextResponse.next();
    
    // Add additional security headers for pages
    response.headers.set(
        'Content-Security-Policy',
        [
            "default-src 'self'",
            "script-src 'self' 'unsafe-eval' 'unsafe-inline'", // Relaxed for Next.js
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
            "font-src 'self' https://fonts.gstatic.com",
            "img-src 'self' data: https:",
            "connect-src 'self' https:",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ].join('; ')
    );

    // Add cache control headers
    if (pathname.startsWith('/dashboard') || pathname.startsWith('/admin')) {
        response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
        response.headers.set('Pragma', 'no-cache');
        response.headers.set('Expires', '0');
    }

    return response;
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - public folder
         */
        '/((?!_next/static|_next/image|favicon.ico|public/).*)',
    ],
};
