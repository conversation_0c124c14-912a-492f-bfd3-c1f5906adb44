# System Config
MONGODB_URI=
REDIS_URL=
REDIS_PREFIX=

# Application Config
NEXT_PUBLIC_APP_NAME=
NEXT_PUBLIC_SHORT_NAME=
NEXT_PUBLIC_DESCRIPTION=
NEXT_PUBLIC_BASE_URL=

# Next Auth Config
NEXTAUTH_URL=
NEXTAUTH_SECRET=

# Cloud Storage Config
CLOUD_STORAGE_APP_ACTIVE=false
CLOUD_STORAGE_APP_USER=
CLOUD_STORAGE_APP_CLIENT=
CLOUD_STORAGE_APP_SECRET=

# Docker Compose
MONGO_ROOT_USERNAME=
MONGO_ROOT_PASSWORD=
MONGO_DATABASE=
REDIS_PASSWORD=
