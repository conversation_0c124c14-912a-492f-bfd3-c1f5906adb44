{"name": "bcbs-dashboard", "version": "1.0.0", "private": true, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"dev": "yarn && yarn format && yarn lint && next dev", "build": "next build", "build:analyze": "BUNDLE_ANALYZER=true next build", "build:production": "NODE_ENV=production next build", "start": "next start", "start:production": "NODE_ENV=production next start", "format": "prettier --check --write \"{app,demo,layout,types,lib}/**/*.{js,ts,tsx,d.ts}\"", "format:fix": "prettier --write \"{app,demo,layout,types,lib}/**/*.{js,ts,tsx,d.ts}\"", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "health-check": "curl -f http://localhost:3000/api/health || exit 1", "docker:build": "docker build -t store-manager .", "docker:run": "docker run -p 3000:3000 store-manager", "docker:compose": "docker-compose up -d", "docker:compose:down": "docker-compose down", "validate-env": "node -e \"require('./lib/env-validation.js')\"", "prestart": "yarn validate-env", "prebuild": "yarn type-check && yarn lint"}, "dependencies": {"@types/node": "^22.14.0", "@types/react": "18.2.12", "@types/react-dom": "18.2.5", "@vercel/analytics": "^1.5.0", "bcryptjs": "^2.4.3", "chart.js": "4.2.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "ioredis": "^5.3.2", "lodash": "^4.17.21", "mongoose": "^8.13.1", "next": "13.4.8", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primereact": "10.2.1", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "^5.8.3", "uuid": "^11.1.0"}, "devDependencies": {"@next/bundle-analyzer": "^13.4.8", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.3", "@types/lodash": "^4.17.16", "@types/mongoose": "^5.11.97", "@types/next-pwa": "^5.6.9", "eslint": "8.43.0", "eslint-config-next": "13.4.6", "jest": "^29.6.1", "jest-environment-jsdom": "^29.6.1", "prettier": "^2.8.8", "sass": "^1.63.4"}}