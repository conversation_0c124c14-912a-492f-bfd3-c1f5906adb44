/**
 * Database Optimization Utilities
 * Provides optimized database operations with caching and connection pooling
 */

import mongoose from 'mongoose';
import { CacheManager } from './redis';
import { withPerformanceLogging } from './performance';
import { env } from './env-validation';

// Database connection pool configuration
export const dbPoolConfig = {
    maxPoolSize: env.MONGODB_MAX_POOL_SIZE,
    minPoolSize: env.MONGODB_MIN_POOL_SIZE,
    maxIdleTimeMS: env.MONGODB_MAX_IDLE_TIME_MS,
    serverSelectionTimeoutMS: env.MONGODB_SERVER_SELECTION_TIMEOUT_MS,
    socketTimeoutMS: 45000,
    bufferCommands: false,
    bufferMaxEntries: 0,
};

// Cache manager instance
const cache = new CacheManager();

// Generic repository pattern with caching
export abstract class BaseRepository<T> {
    protected model: mongoose.Model<T>;
    protected cachePrefix: string;
    protected defaultCacheTTL: number = 300; // 5 minutes

    constructor(model: mongoose.Model<T>, cachePrefix: string) {
        this.model = model;
        this.cachePrefix = cachePrefix;
    }

    // Generate cache key
    protected getCacheKey(operation: string, params?: any): string {
        const paramString = params ? JSON.stringify(params) : '';
        return `${this.cachePrefix}:${operation}:${paramString}`;
    }

    // Find by ID with caching
    async findById(id: string, useCache: boolean = true): Promise<T | null> {
        const cacheKey = this.getCacheKey('findById', { id });
        
        if (useCache) {
            const cached = await cache.get<T>(cacheKey);
            if (cached) return cached;
        }

        const result = await withPerformanceLogging(
            () => this.model.findById(id).lean().exec(),
            `${this.cachePrefix}.findById`
        )();

        if (result && useCache) {
            await cache.set(cacheKey, result, this.defaultCacheTTL);
        }

        return result;
    }

    // Find with pagination and caching
    async findPaginated(
        filter: any = {},
        page: number = 1,
        limit: number = 10,
        sort: any = { createdAt: -1 },
        useCache: boolean = true
    ): Promise<{ data: T[]; total: number; page: number; totalPages: number }> {
        const cacheKey = this.getCacheKey('findPaginated', { filter, page, limit, sort });
        
        if (useCache) {
            const cached = await cache.get<any>(cacheKey);
            if (cached) return cached;
        }

        const skip = (page - 1) * limit;
        
        const [data, total] = await Promise.all([
            withPerformanceLogging(
                () => this.model.find(filter).sort(sort).skip(skip).limit(limit).lean().exec(),
                `${this.cachePrefix}.findPaginated.data`
            )(),
            withPerformanceLogging(
                () => this.model.countDocuments(filter).exec(),
                `${this.cachePrefix}.findPaginated.count`
            )()
        ]);

        const result = {
            data,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };

        if (useCache) {
            await cache.set(cacheKey, result, this.defaultCacheTTL);
        }

        return result;
    }

    // Create with cache invalidation
    async create(data: Partial<T>): Promise<T> {
        const result = await withPerformanceLogging(
            () => this.model.create(data),
            `${this.cachePrefix}.create`
        )();

        // Invalidate related caches
        await this.invalidateCache();

        return result;
    }

    // Update with cache invalidation
    async updateById(id: string, data: Partial<T>): Promise<T | null> {
        const result = await withPerformanceLogging(
            () => this.model.findByIdAndUpdate(id, data, { new: true }).lean().exec(),
            `${this.cachePrefix}.updateById`
        )();

        // Invalidate specific and related caches
        await this.invalidateCache(id);

        return result;
    }

    // Delete with cache invalidation
    async deleteById(id: string): Promise<boolean> {
        const result = await withPerformanceLogging(
            () => this.model.findByIdAndDelete(id).exec(),
            `${this.cachePrefix}.deleteById`
        )();

        // Invalidate specific and related caches
        await this.invalidateCache(id);

        return !!result;
    }

    // Bulk operations
    async bulkCreate(data: Partial<T>[]): Promise<T[]> {
        const result = await withPerformanceLogging(
            () => this.model.insertMany(data),
            `${this.cachePrefix}.bulkCreate`
        )();

        await this.invalidateCache();
        return result;
    }

    // Search with full-text search and caching
    async search(
        query: string,
        fields: string[],
        page: number = 1,
        limit: number = 10,
        useCache: boolean = true
    ): Promise<{ data: T[]; total: number; page: number; totalPages: number }> {
        const cacheKey = this.getCacheKey('search', { query, fields, page, limit });
        
        if (useCache) {
            const cached = await cache.get<any>(cacheKey);
            if (cached) return cached;
        }

        const searchFilter = {
            $or: fields.map(field => ({
                [field]: { $regex: query, $options: 'i' }
            }))
        };

        return this.findPaginated(searchFilter, page, limit, { score: { $meta: 'textScore' } }, false);
    }

    // Aggregate with caching
    async aggregate(pipeline: any[], useCache: boolean = true, cacheTTL?: number): Promise<any[]> {
        const cacheKey = this.getCacheKey('aggregate', { pipeline });
        
        if (useCache) {
            const cached = await cache.get<any[]>(cacheKey);
            if (cached) return cached;
        }

        const result = await withPerformanceLogging(
            () => this.model.aggregate(pipeline).exec(),
            `${this.cachePrefix}.aggregate`
        )();

        if (useCache) {
            await cache.set(cacheKey, result, cacheTTL || this.defaultCacheTTL);
        }

        return result;
    }

    // Cache invalidation
    protected async invalidateCache(id?: string): Promise<void> {
        // Invalidate specific item cache
        if (id) {
            const specificKey = this.getCacheKey('findById', { id });
            await cache.del(specificKey);
        }

        // Invalidate list caches (this is a simple approach, could be more sophisticated)
        // In a real implementation, you might want to use cache tags or patterns
        console.log(`Cache invalidated for ${this.cachePrefix}`);
    }

    // Get statistics
    async getStats(): Promise<any> {
        const cacheKey = this.getCacheKey('stats', {});
        const cached = await cache.get<any>(cacheKey);
        if (cached) return cached;

        const stats = await this.model.collection.stats();
        await cache.set(cacheKey, stats, 3600); // Cache for 1 hour

        return stats;
    }
}

// Database health monitoring
export class DatabaseMonitor {
    static async getConnectionStatus(): Promise<{
        status: string;
        readyState: number;
        host: string;
        port: number;
        name: string;
    }> {
        const connection = mongoose.connection;
        return {
            status: this.getReadyStateString(connection.readyState),
            readyState: connection.readyState,
            host: connection.host || 'unknown',
            port: connection.port || 0,
            name: connection.name || 'unknown'
        };
    }

    static async getPerformanceMetrics(): Promise<{
        activeConnections: number;
        availableConnections: number;
        totalConnections: number;
        operationsCount: number;
    }> {
        const db = mongoose.connection.db;
        if (!db) {
            throw new Error('Database not connected');
        }

        const serverStatus = await db.admin().serverStatus();
        return {
            activeConnections: serverStatus.connections?.current || 0,
            availableConnections: serverStatus.connections?.available || 0,
            totalConnections: serverStatus.connections?.totalCreated || 0,
            operationsCount: serverStatus.opcounters?.query || 0
        };
    }

    private static getReadyStateString(state: number): string {
        const states = {
            0: 'disconnected',
            1: 'connected',
            2: 'connecting',
            3: 'disconnecting'
        };
        return states[state as keyof typeof states] || 'unknown';
    }
}

// Query optimization utilities
export class QueryOptimizer {
    // Suggest indexes based on query patterns
    static analyzeQuery(model: mongoose.Model<any>, filter: any): string[] {
        const suggestions: string[] = [];
        
        Object.keys(filter).forEach(key => {
            if (key.includes('.')) {
                suggestions.push(`Consider adding compound index for: ${key}`);
            } else {
                suggestions.push(`Consider adding single field index for: ${key}`);
            }
        });

        return suggestions;
    }

    // Explain query execution
    static async explainQuery(model: mongoose.Model<any>, filter: any): Promise<any> {
        return model.find(filter).explain('executionStats');
    }
}
