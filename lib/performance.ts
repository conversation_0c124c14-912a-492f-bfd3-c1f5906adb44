/**
 * Performance Optimization Utilities
 * Provides utilities for monitoring and optimizing application performance
 */

import React from 'react';
import { env } from './env-validation';

// Performance monitoring
export class PerformanceMonitor {
    private static instance: PerformanceMonitor;
    private metrics: Map<string, number> = new Map();

    static getInstance(): PerformanceMonitor {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor();
        }
        return PerformanceMonitor.instance;
    }

    // Start timing an operation
    startTiming(operation: string): void {
        this.metrics.set(operation, performance.now());
    }

    // End timing and return duration
    endTiming(operation: string): number {
        const startTime = this.metrics.get(operation);
        if (!startTime) {
            console.warn(`No start time found for operation: ${operation}`);
            return 0;
        }

        const duration = performance.now() - startTime;
        this.metrics.delete(operation);
        
        // Log slow operations in production
        if (env.NODE_ENV === 'production' && duration > 1000) {
            console.warn(`Slow operation detected: ${operation} took ${duration.toFixed(2)}ms`);
        }

        return duration;
    }

    // Get current metrics
    getMetrics(): Record<string, number> {
        return Object.fromEntries(this.metrics);
    }
}

// Database query optimization
export function withPerformanceLogging<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    operationName: string
): T {
    return (async (...args: Parameters<T>) => {
        const monitor = PerformanceMonitor.getInstance();
        monitor.startTiming(operationName);
        
        try {
            const result = await fn(...args);
            const duration = monitor.endTiming(operationName);
            
            if (env.NODE_ENV === 'development') {
                console.log(`${operationName} completed in ${duration.toFixed(2)}ms`);
            }
            
            return result;
        } catch (error) {
            monitor.endTiming(operationName);
            throw error;
        }
    }) as T;
}

// Memory usage monitoring
export function getMemoryUsage() {
    const usage = process.memoryUsage();
    return {
        rss: Math.round(usage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
        external: Math.round(usage.external / 1024 / 1024), // MB
        arrayBuffers: Math.round(usage.arrayBuffers / 1024 / 1024), // MB
    };
}

// Cache utilities for better performance
export class CacheUtils {
    private static cache = new Map<string, { data: any; expiry: number }>();

    static set(key: string, data: any, ttlSeconds: number = 300): void {
        const expiry = Date.now() + (ttlSeconds * 1000);
        this.cache.set(key, { data, expiry });
    }

    static get<T>(key: string): T | null {
        const item = this.cache.get(key);
        if (!item) return null;

        if (Date.now() > item.expiry) {
            this.cache.delete(key);
            return null;
        }

        return item.data as T;
    }

    static delete(key: string): boolean {
        return this.cache.delete(key);
    }

    static clear(): void {
        this.cache.clear();
    }

    static size(): number {
        return this.cache.size;
    }

    // Clean expired entries
    static cleanup(): void {
        const now = Date.now();
        for (const [key, item] of this.cache.entries()) {
            if (now > item.expiry) {
                this.cache.delete(key);
            }
        }
    }
}

// Debounce utility for performance
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
}

// Throttle utility for performance
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Lazy loading utility
export function createLazyComponent<T>(
    importFn: () => Promise<{ default: T }>
): React.LazyExoticComponent<T> {
    return React.lazy(importFn);
}

// Bundle size analyzer utility
export function analyzeBundleSize() {
    if (typeof window !== 'undefined' && env.NODE_ENV === 'development') {
        // Client-side bundle analysis
        const scripts = Array.from(document.querySelectorAll('script[src]'));
        const totalSize = scripts.reduce((acc, script) => {
            const src = script.getAttribute('src');
            if (src && src.includes('_next/static')) {
                // Estimate size based on script tag (this is approximate)
                return acc + 1;
            }
            return acc;
        }, 0);
        
        console.log(`Estimated bundle scripts: ${totalSize}`);
    }
}

// Performance observer for Core Web Vitals
export function initPerformanceObserver() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
        // Largest Contentful Paint
        const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            console.log('LCP:', lastEntry.startTime);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // First Input Delay
        const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry) => {
                console.log('FID:', entry.processingStart - entry.startTime);
            });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

        // Cumulative Layout Shift
        const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0;
            const entries = list.getEntries();
            entries.forEach((entry) => {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            });
            console.log('CLS:', clsValue);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
}

// Cleanup expired cache entries every 5 minutes
if (typeof window === 'undefined') {
    setInterval(() => {
        CacheUtils.cleanup();
    }, 5 * 60 * 1000);
}
