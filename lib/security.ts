/**
 * Security Utilities and Middleware
 * Comprehensive security measures for production deployment
 */

import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { env } from './env-validation';
import { CacheManager } from './redis';

// Rate limiting configuration
interface RateLimitConfig {
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
}

// Rate limiter class
export class RateLimiter {
    private cache: CacheManager;
    private config: RateLimitConfig;

    constructor(config: RateLimitConfig) {
        this.cache = new CacheManager();
        this.config = config;
    }

    async isAllowed(identifier: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
        const key = `rate_limit:${identifier}`;
        const now = Date.now();
        const windowStart = now - this.config.windowMs;

        // Get current request count
        const requests = await this.cache.get<number[]>(key) || [];
        
        // Filter requests within the current window
        const validRequests = requests.filter(timestamp => timestamp > windowStart);
        
        // Check if limit exceeded
        const allowed = validRequests.length < this.config.maxRequests;
        
        if (allowed) {
            // Add current request
            validRequests.push(now);
            await this.cache.set(key, validRequests, Math.ceil(this.config.windowMs / 1000));
        }

        return {
            allowed,
            remaining: Math.max(0, this.config.maxRequests - validRequests.length),
            resetTime: windowStart + this.config.windowMs
        };
    }
}

// Create rate limiters for different endpoints
export const rateLimiters = {
    api: new RateLimiter({
        windowMs: env.RATE_LIMIT_WINDOW_MS,
        maxRequests: env.RATE_LIMIT_MAX
    }),
    auth: new RateLimiter({
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxRequests: 5 // 5 login attempts per 15 minutes
    }),
    upload: new RateLimiter({
        windowMs: 60 * 1000, // 1 minute
        maxRequests: 10 // 10 uploads per minute
    })
};

// Security headers configuration
export const securityHeaders = {
    'X-DNS-Prefetch-Control': 'off',
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
    'Content-Security-Policy': generateCSP(),
};

// Generate Content Security Policy
function generateCSP(): string {
    const nonce = crypto.randomBytes(16).toString('base64');
    
    const csp = [
        "default-src 'self'",
        `script-src 'self' 'nonce-${nonce}' 'strict-dynamic'`,
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
        "font-src 'self' https://fonts.gstatic.com",
        "img-src 'self' data: https:",
        "connect-src 'self' https:",
        "frame-ancestors 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        env.CSP_REPORT_URI ? `report-uri ${env.CSP_REPORT_URI}` : ''
    ].filter(Boolean).join('; ');

    return csp;
}

// Input validation and sanitization
export class InputValidator {
    // Sanitize string input
    static sanitizeString(input: string): string {
        return input
            .replace(/[<>]/g, '') // Remove potential HTML tags
            .replace(/javascript:/gi, '') // Remove javascript: protocol
            .replace(/on\w+=/gi, '') // Remove event handlers
            .trim();
    }

    // Validate email format
    static isValidEmail(email: string): boolean {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Validate phone number
    static isValidPhone(phone: string): boolean {
        const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
        return phoneRegex.test(phone);
    }

    // Validate password strength
    static validatePassword(password: string): { valid: boolean; errors: string[] } {
        const errors: string[] = [];
        
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        }
        
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        
        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    // Sanitize object recursively
    static sanitizeObject(obj: any): any {
        if (typeof obj === 'string') {
            return this.sanitizeString(obj);
        }
        
        if (Array.isArray(obj)) {
            return obj.map(item => this.sanitizeObject(item));
        }
        
        if (obj && typeof obj === 'object') {
            const sanitized: any = {};
            for (const [key, value] of Object.entries(obj)) {
                sanitized[key] = this.sanitizeObject(value);
            }
            return sanitized;
        }
        
        return obj;
    }
}

// CSRF protection
export class CSRFProtection {
    private static tokens = new Map<string, { token: string; expiry: number }>();

    static generateToken(sessionId: string): string {
        const token = crypto.randomBytes(32).toString('hex');
        const expiry = Date.now() + (60 * 60 * 1000); // 1 hour
        
        this.tokens.set(sessionId, { token, expiry });
        
        // Clean up expired tokens
        this.cleanupExpiredTokens();
        
        return token;
    }

    static validateToken(sessionId: string, token: string): boolean {
        const stored = this.tokens.get(sessionId);
        
        if (!stored || stored.expiry < Date.now()) {
            this.tokens.delete(sessionId);
            return false;
        }
        
        return stored.token === token;
    }

    private static cleanupExpiredTokens(): void {
        const now = Date.now();
        for (const [sessionId, { expiry }] of this.tokens.entries()) {
            if (expiry < now) {
                this.tokens.delete(sessionId);
            }
        }
    }
}

// Security middleware
export function createSecurityMiddleware() {
    return async function securityMiddleware(request: NextRequest) {
        const response = NextResponse.next();
        
        // Add security headers
        Object.entries(securityHeaders).forEach(([key, value]) => {
            response.headers.set(key, value);
        });

        // Rate limiting
        const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
        const rateLimitResult = await rateLimiters.api.isAllowed(clientIP);
        
        if (!rateLimitResult.allowed) {
            return new NextResponse('Too Many Requests', {
                status: 429,
                headers: {
                    'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString(),
                    'X-RateLimit-Limit': env.RATE_LIMIT_MAX.toString(),
                    'X-RateLimit-Remaining': '0',
                    'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
                }
            });
        }

        // Add rate limit headers
        response.headers.set('X-RateLimit-Limit', env.RATE_LIMIT_MAX.toString());
        response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
        response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.toString());

        return response;
    };
}

// Password hashing utilities
export class PasswordUtils {
    static async hash(password: string): Promise<string> {
        const bcrypt = await import('bcryptjs');
        return bcrypt.hash(password, env.BCRYPT_ROUNDS);
    }

    static async verify(password: string, hash: string): Promise<boolean> {
        const bcrypt = await import('bcryptjs');
        return bcrypt.compare(password, hash);
    }

    static generateSecurePassword(length: number = 16): string {
        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        let password = '';
        
        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        
        return password;
    }
}

// Encryption utilities
export class EncryptionUtils {
    private static algorithm = 'aes-256-gcm';
    private static keyLength = 32;

    static encrypt(text: string, key?: string): { encrypted: string; iv: string; tag: string } {
        const secretKey = key || env.JWT_SECRET || 'default-secret-key';
        const derivedKey = crypto.scryptSync(secretKey, 'salt', this.keyLength);
        const iv = crypto.randomBytes(16);
        
        const cipher = crypto.createCipher(this.algorithm, derivedKey);
        cipher.setAAD(Buffer.from('additional-data'));
        
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        const tag = cipher.getAuthTag();
        
        return {
            encrypted,
            iv: iv.toString('hex'),
            tag: tag.toString('hex')
        };
    }

    static decrypt(encryptedData: { encrypted: string; iv: string; tag: string }, key?: string): string {
        const secretKey = key || env.JWT_SECRET || 'default-secret-key';
        const derivedKey = crypto.scryptSync(secretKey, 'salt', this.keyLength);
        
        const decipher = crypto.createDecipher(this.algorithm, derivedKey);
        decipher.setAAD(Buffer.from('additional-data'));
        decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));
        
        let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
    }
}
