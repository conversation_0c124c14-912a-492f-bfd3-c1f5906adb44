/**
 * Environment Variable Validation
 * Validates and provides type-safe access to environment variables
 */

import { z } from 'zod';

// Define the schema for environment variables
const envSchema = z.object({
    // Node Environment
    NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
    PORT: z.string().transform(Number).pipe(z.number().min(1).max(65535)).default('3000'),

    // Database Configuration
    MONGODB_URI: z.string().min(1, 'MongoDB URI is required'),
    MONGODB_MAX_POOL_SIZE: z.string().transform(Number).pipe(z.number().min(1)).default('10'),
    MONGODB_MIN_POOL_SIZE: z.string().transform(Number).pipe(z.number().min(1)).default('5'),
    MONGODB_MAX_IDLE_TIME_MS: z.string().transform(Number).pipe(z.number().min(1000)).default('30000'),
    MONGODB_SERVER_SELECTION_TIMEOUT_MS: z.string().transform(Number).pipe(z.number().min(1000)).default('5000'),

    // Redis Configuration
    REDIS_URL: z.string().min(1, 'Redis URL is required'),
    REDIS_PREFIX: z.string().default('store_manager:'),
    REDIS_MAX_RETRIES: z.string().transform(Number).pipe(z.number().min(0)).default('3'),
    REDIS_RETRY_DELAY_ON_FAILURE_MS: z.string().transform(Number).pipe(z.number().min(50)).default('100'),

    // Application Configuration
    NEXT_PUBLIC_APP_NAME: z.string().min(1, 'App name is required'),
    NEXT_PUBLIC_SHORT_NAME: z.string().min(1, 'Short name is required'),
    NEXT_PUBLIC_DESCRIPTION: z.string().min(1, 'Description is required'),
    NEXT_PUBLIC_BASE_URL: z.string().url('Base URL must be a valid URL'),
    NEXT_PUBLIC_API_URL: z.string().url('API URL must be a valid URL').optional(),

    // Authentication & Security
    NEXTAUTH_URL: z.string().url('NextAuth URL must be a valid URL'),
    NEXTAUTH_SECRET: z.string().min(32, 'NextAuth secret must be at least 32 characters'),
    JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters').optional(),
    BCRYPT_ROUNDS: z.string().transform(Number).pipe(z.number().min(10).max(15)).default('12'),

    // Session Configuration
    SESSION_MAX_AGE: z.string().transform(Number).pipe(z.number().min(300)).default('86400'),
    SESSION_UPDATE_AGE: z.string().transform(Number).pipe(z.number().min(60)).default('3600'),

    // Security Headers & CORS
    ALLOWED_ORIGINS: z.string().optional(),
    CSP_REPORT_URI: z.string().url().optional(),
    RATE_LIMIT_MAX: z.string().transform(Number).pipe(z.number().min(1)).default('100'),
    RATE_LIMIT_WINDOW_MS: z.string().transform(Number).pipe(z.number().min(1000)).default('900000'),

    // Cloud Storage & CDN
    CLOUD_STORAGE_APP_ACTIVE: z.string().transform(val => val === 'true').default('false'),
    CLOUD_STORAGE_APP_USER: z.string().optional(),
    CLOUD_STORAGE_APP_CLIENT: z.string().optional(),
    CLOUD_STORAGE_APP_SECRET: z.string().optional(),
    CDN_URL: z.string().url().optional(),

    // Monitoring & Logging
    SENTRY_DSN: z.string().url().optional(),
    SENTRY_ENVIRONMENT: z.string().optional(),
    SENTRY_RELEASE: z.string().optional(),
    GOOGLE_ANALYTICS_ID: z.string().optional(),
    VERCEL_ANALYTICS_ID: z.string().optional(),
    LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    LOG_FORMAT: z.enum(['json', 'pretty']).default('json'),

    // Email & Notifications
    SMTP_HOST: z.string().optional(),
    SMTP_PORT: z.string().transform(Number).pipe(z.number().min(1).max(65535)).optional(),
    SMTP_USER: z.string().optional(),
    SMTP_PASS: z.string().optional(),
    SMTP_FROM: z.string().email().optional(),

    // Performance & Optimization
    ENABLE_COMPRESSION: z.string().transform(val => val === 'true').default('true'),
    ENABLE_STATIC_OPTIMIZATION: z.string().transform(val => val === 'true').default('true'),
    IMAGE_OPTIMIZATION: z.string().transform(val => val === 'true').default('true'),
    BUNDLE_ANALYZER: z.string().transform(val => val === 'true').default('false'),

    // Feature Flags
    ENABLE_PWA: z.string().transform(val => val === 'true').default('true'),
    ENABLE_OFFLINE_MODE: z.string().transform(val => val === 'true').default('true'),
    ENABLE_PUSH_NOTIFICATIONS: z.string().transform(val => val === 'true').default('false'),
    ENABLE_ANALYTICS: z.string().transform(val => val === 'true').default('true'),
    ENABLE_ERROR_REPORTING: z.string().transform(val => val === 'true').default('true'),
});

// Validate environment variables
function validateEnv() {
    try {
        return envSchema.parse(process.env);
    } catch (error) {
        if (error instanceof z.ZodError) {
            const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
            throw new Error(`Environment validation failed:\n${missingVars.join('\n')}`);
        }
        throw error;
    }
}

// Export validated environment variables
export const env = validateEnv();

// Type for environment variables
export type Env = z.infer<typeof envSchema>;

// Helper function to check if we're in production
export const isProduction = env.NODE_ENV === 'production';
export const isDevelopment = env.NODE_ENV === 'development';
export const isTest = env.NODE_ENV === 'test';

// Helper function to get database configuration
export const getDatabaseConfig = () => ({
    uri: env.MONGODB_URI,
    options: {
        maxPoolSize: env.MONGODB_MAX_POOL_SIZE,
        minPoolSize: env.MONGODB_MIN_POOL_SIZE,
        maxIdleTimeMS: env.MONGODB_MAX_IDLE_TIME_MS,
        serverSelectionTimeoutMS: env.MONGODB_SERVER_SELECTION_TIMEOUT_MS,
    },
});

// Helper function to get Redis configuration
export const getRedisConfig = () => ({
    url: env.REDIS_URL,
    keyPrefix: env.REDIS_PREFIX,
    maxRetriesPerRequest: env.REDIS_MAX_RETRIES,
    retryDelayOnFailover: env.REDIS_RETRY_DELAY_ON_FAILURE_MS,
});
