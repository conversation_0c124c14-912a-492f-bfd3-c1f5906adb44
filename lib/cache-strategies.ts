/**
 * Advanced Caching Strategies
 * Implements various caching patterns for optimal performance
 */

import { CacheManager } from './redis';
import { env } from './env-validation';

// Cache configuration
export const CACHE_CONFIG = {
    // TTL values in seconds
    TTL: {
        SHORT: 60,           // 1 minute
        MEDIUM: 300,         // 5 minutes
        LONG: 1800,          // 30 minutes
        VERY_LONG: 3600,     // 1 hour
        DAILY: 86400,        // 24 hours
    },
    
    // Cache keys
    KEYS: {
        USER_SESSION: 'user:session',
        USER_PROFILE: 'user:profile',
        PRODUCT_LIST: 'product:list',
        PRODUCT_DETAIL: 'product:detail',
        CATEGORY_LIST: 'category:list',
        DASHBOARD_STATS: 'dashboard:stats',
        SYSTEM_CONFIG: 'system:config',
    },
    
    // Cache tags for invalidation
    TAGS: {
        USER: 'user',
        PRODUCT: 'product',
        CATEGORY: 'category',
        ORDER: 'order',
        SYSTEM: 'system',
    }
};

// Cache strategy interface
interface CacheStrategy {
    get<T>(key: string): Promise<T | null>;
    set(key: string, value: any, ttl?: number): Promise<boolean>;
    invalidate(pattern: string): Promise<boolean>;
}

// Write-through cache strategy
export class WriteThroughCache implements CacheStrategy {
    private cache: CacheManager;

    constructor() {
        this.cache = new CacheManager();
    }

    async get<T>(key: string): Promise<T | null> {
        return this.cache.get<T>(key);
    }

    async set(key: string, value: any, ttl: number = CACHE_CONFIG.TTL.MEDIUM): Promise<boolean> {
        return this.cache.set(key, value, ttl);
    }

    async invalidate(pattern: string): Promise<boolean> {
        // In a real implementation, you'd use Redis SCAN with pattern matching
        // For now, we'll implement a simple key deletion
        return this.cache.del(pattern);
    }

    // Write-through: update cache and database simultaneously
    async writeThrough<T>(
        key: string,
        value: T,
        dbUpdateFn: () => Promise<T>,
        ttl: number = CACHE_CONFIG.TTL.MEDIUM
    ): Promise<T> {
        // Update database first
        const result = await dbUpdateFn();
        
        // Then update cache
        await this.set(key, result, ttl);
        
        return result;
    }
}

// Write-behind (write-back) cache strategy
export class WriteBehindCache implements CacheStrategy {
    private cache: CacheManager;
    private writeQueue: Map<string, { value: any; timestamp: number }> = new Map();
    private flushInterval: number = 5000; // 5 seconds

    constructor() {
        this.cache = new CacheManager();
        this.startFlushTimer();
    }

    async get<T>(key: string): Promise<T | null> {
        // Check write queue first
        const queued = this.writeQueue.get(key);
        if (queued) {
            return queued.value;
        }
        
        return this.cache.get<T>(key);
    }

    async set(key: string, value: any, ttl: number = CACHE_CONFIG.TTL.MEDIUM): Promise<boolean> {
        // Add to write queue
        this.writeQueue.set(key, { value, timestamp: Date.now() });
        
        // Update cache immediately
        return this.cache.set(key, value, ttl);
    }

    async invalidate(pattern: string): Promise<boolean> {
        this.writeQueue.delete(pattern);
        return this.cache.del(pattern);
    }

    // Write-behind: update cache immediately, database later
    async writeBehind<T>(
        key: string,
        value: T,
        dbUpdateFn: () => Promise<T>,
        ttl: number = CACHE_CONFIG.TTL.MEDIUM
    ): Promise<T> {
        // Update cache immediately
        await this.set(key, value, ttl);
        
        // Queue database update
        this.writeQueue.set(key, { 
            value: { data: value, updateFn: dbUpdateFn }, 
            timestamp: Date.now() 
        });
        
        return value;
    }

    private startFlushTimer(): void {
        setInterval(async () => {
            await this.flushWriteQueue();
        }, this.flushInterval);
    }

    private async flushWriteQueue(): Promise<void> {
        const now = Date.now();
        const promises: Promise<any>[] = [];

        for (const [key, { value, timestamp }] of this.writeQueue.entries()) {
            // Flush items older than flush interval
            if (now - timestamp >= this.flushInterval) {
                if (value.updateFn && typeof value.updateFn === 'function') {
                    promises.push(
                        value.updateFn().catch((error: any) => {
                            console.error(`Failed to flush cache key ${key}:`, error);
                        })
                    );
                }
                this.writeQueue.delete(key);
            }
        }

        if (promises.length > 0) {
            await Promise.allSettled(promises);
        }
    }
}

// Cache-aside (lazy loading) strategy
export class CacheAsideStrategy implements CacheStrategy {
    private cache: CacheManager;

    constructor() {
        this.cache = new CacheManager();
    }

    async get<T>(key: string): Promise<T | null> {
        return this.cache.get<T>(key);
    }

    async set(key: string, value: any, ttl: number = CACHE_CONFIG.TTL.MEDIUM): Promise<boolean> {
        return this.cache.set(key, value, ttl);
    }

    async invalidate(pattern: string): Promise<boolean> {
        return this.cache.del(pattern);
    }

    // Cache-aside: check cache first, load from DB if miss
    async getOrLoad<T>(
        key: string,
        loadFn: () => Promise<T>,
        ttl: number = CACHE_CONFIG.TTL.MEDIUM
    ): Promise<T> {
        // Try cache first
        const cached = await this.get<T>(key);
        if (cached !== null) {
            return cached;
        }

        // Load from database
        const data = await loadFn();
        
        // Store in cache
        await this.set(key, data, ttl);
        
        return data;
    }
}

// Multi-level cache strategy
export class MultiLevelCache {
    private l1Cache: Map<string, { value: any; expiry: number }> = new Map(); // Memory cache
    private l2Cache: CacheManager; // Redis cache
    private l1TTL: number = 60; // 1 minute for L1
    private l2TTL: number = 300; // 5 minutes for L2

    constructor() {
        this.l2Cache = new CacheManager();
        this.startCleanupTimer();
    }

    async get<T>(key: string): Promise<T | null> {
        // Check L1 cache (memory) first
        const l1Item = this.l1Cache.get(key);
        if (l1Item && Date.now() < l1Item.expiry) {
            return l1Item.value;
        }

        // Check L2 cache (Redis)
        const l2Item = await this.l2Cache.get<T>(key);
        if (l2Item !== null) {
            // Promote to L1 cache
            this.l1Cache.set(key, {
                value: l2Item,
                expiry: Date.now() + (this.l1TTL * 1000)
            });
            return l2Item;
        }

        return null;
    }

    async set(key: string, value: any, ttl?: number): Promise<boolean> {
        const l1Expiry = Date.now() + (this.l1TTL * 1000);
        const l2TTL = ttl || this.l2TTL;

        // Set in both caches
        this.l1Cache.set(key, { value, expiry: l1Expiry });
        return this.l2Cache.set(key, value, l2TTL);
    }

    async invalidate(key: string): Promise<boolean> {
        this.l1Cache.delete(key);
        return this.l2Cache.del(key);
    }

    private startCleanupTimer(): void {
        setInterval(() => {
            const now = Date.now();
            for (const [key, item] of this.l1Cache.entries()) {
                if (now >= item.expiry) {
                    this.l1Cache.delete(key);
                }
            }
        }, 30000); // Clean up every 30 seconds
    }
}

// Cache warming utility
export class CacheWarmer {
    private strategies: Map<string, CacheStrategy> = new Map();

    addStrategy(name: string, strategy: CacheStrategy): void {
        this.strategies.set(name, strategy);
    }

    async warmCache(warmingTasks: Array<{
        strategy: string;
        key: string;
        loadFn: () => Promise<any>;
        ttl?: number;
    }>): Promise<void> {
        const promises = warmingTasks.map(async (task) => {
            const strategy = this.strategies.get(task.strategy);
            if (!strategy) {
                console.warn(`Cache strategy ${task.strategy} not found`);
                return;
            }

            try {
                const data = await task.loadFn();
                await strategy.set(task.key, data, task.ttl);
                console.log(`Cache warmed for key: ${task.key}`);
            } catch (error) {
                console.error(`Failed to warm cache for key ${task.key}:`, error);
            }
        });

        await Promise.allSettled(promises);
    }
}

// Cache factory
export class CacheFactory {
    static createStrategy(type: 'write-through' | 'write-behind' | 'cache-aside' | 'multi-level'): CacheStrategy {
        switch (type) {
            case 'write-through':
                return new WriteThroughCache();
            case 'write-behind':
                return new WriteBehindCache();
            case 'cache-aside':
                return new CacheAsideStrategy();
            case 'multi-level':
                return new MultiLevelCache();
            default:
                throw new Error(`Unknown cache strategy: ${type}`);
        }
    }
}

// Export default cache instance based on environment
export const defaultCache = env.NODE_ENV === 'production' 
    ? CacheFactory.createStrategy('multi-level')
    : CacheFactory.createStrategy('cache-aside');
