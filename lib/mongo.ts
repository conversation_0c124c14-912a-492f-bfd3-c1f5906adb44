'use server';

import { MongooseCache } from '@/types';
import mongoose from 'mongoose';
import { getDatabaseConfig, env, isProduction } from './env-validation';

let cached: MongooseCache = { conn: null, promise: null };

async function connectDB(): Promise<typeof mongoose> {
    const dbConfig = getDatabaseConfig();

    if (cached.conn) {
        // Check if connection is still alive
        if (mongoose.connection.readyState === 1) {
            return cached.conn;
        }
        // Reset cache if connection is dead
        cached = { conn: null, promise: null };
    }

    if (!cached.promise) {
        const mongooseOptions = {
            ...dbConfig.options,
            // Production optimizations
            bufferCommands: false, // Disable mongoose buffering
            bufferMaxEntries: 0, // Disable mongoose buffering
            useNewUrlParser: true,
            useUnifiedTopology: true,
            // Connection pool settings
            maxPoolSize: dbConfig.options.maxPoolSize,
            minPoolSize: dbConfig.options.minPoolSize,
            maxIdleTimeMS: dbConfig.options.maxIdleTimeMS,
            serverSelectionTimeoutMS: dbConfig.options.serverSelectionTimeoutMS,
            // Additional production settings
            socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
            family: 4, // Use IPv4, skip trying IPv6
        };

        if (isProduction) {
            // Production-specific settings
            mongooseOptions.retryWrites = true;
            mongooseOptions.w = 'majority';
        }

        cached.promise = mongoose.connect(dbConfig.uri, mongooseOptions);

        // Add connection event listeners
        mongoose.connection.on('connected', () => {
            console.log('MongoDB connected successfully');
        });

        mongoose.connection.on('error', (err) => {
            console.error('MongoDB connection error:', err);
            cached = { conn: null, promise: null };
        });

        mongoose.connection.on('disconnected', () => {
            console.log('MongoDB disconnected');
            cached = { conn: null, promise: null };
        });

        // Graceful shutdown
        process.on('SIGINT', async () => {
            await mongoose.connection.close();
            process.exit(0);
        });
    }

    try {
        cached.conn = await cached.promise;
        return cached.conn;
    } catch (error) {
        cached = { conn: null, promise: null };
        throw error;
    }
}

export default connectDB;

// Export connection health check
export async function checkDatabaseHealth(): Promise<boolean> {
    try {
        if (mongoose.connection.readyState !== 1) {
            return false;
        }
        await mongoose.connection.db.admin().ping();
        return true;
    } catch {
        return false;
    }
}
