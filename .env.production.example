# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains production-specific environment variables
# Copy this to .env.production and fill in actual values

# =============================================================================
# NODE ENVIRONMENT
# =============================================================================
NODE_ENV=production
PORT=3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MONGODB_URI=*********************************************************************************
MONGODB_MAX_POOL_SIZE=20
MONGODB_MIN_POOL_SIZE=5
MONGODB_MAX_IDLE_TIME_MS=30000
MONGODB_SERVER_SELECTION_TIMEOUT_MS=5000

REDIS_URL=redis://:your-redis-password@redis:6379
REDIS_PREFIX=store_manager:prod:
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY_ON_FAILURE_MS=100

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NEXT_PUBLIC_APP_NAME=Store Manager
NEXT_PUBLIC_SHORT_NAME=StoreManager
NEXT_PUBLIC_DESCRIPTION=Professional Store Management System
NEXT_PUBLIC_BASE_URL=https://store-manager.your-domain.com
NEXT_PUBLIC_API_URL=https://store-manager.your-domain.com/api

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
NEXTAUTH_URL=https://store-manager.your-domain.com
NEXTAUTH_SECRET=your-super-secret-jwt-secret-min-32-chars-production
JWT_SECRET=your-jwt-secret-for-custom-tokens-production
BCRYPT_ROUNDS=12

SESSION_MAX_AGE=86400
SESSION_UPDATE_AGE=3600

# =============================================================================
# SECURITY HEADERS & CORS
# =============================================================================
ALLOWED_ORIGINS=https://store-manager.your-domain.com
CSP_REPORT_URI=https://store-manager.your-domain.com/api/csp-report
RATE_LIMIT_MAX=1000
RATE_LIMIT_WINDOW_MS=900000

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=production
SENTRY_RELEASE=1.0.0

GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
VERCEL_ANALYTICS_ID=your-vercel-analytics-id

LOG_LEVEL=warn
LOG_FORMAT=json

# =============================================================================
# EMAIL & NOTIFICATIONS
# =============================================================================
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASS=your-smtp-password
SMTP_FROM=<EMAIL>

# =============================================================================
# PERFORMANCE & OPTIMIZATION
# =============================================================================
ENABLE_COMPRESSION=true
ENABLE_STATIC_OPTIMIZATION=true
IMAGE_OPTIMIZATION=true
BUNDLE_ANALYZER=false

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_PWA=true
ENABLE_OFFLINE_MODE=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=true
ENABLE_ERROR_REPORTING=true
