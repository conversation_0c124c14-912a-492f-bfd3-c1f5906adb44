/** @type {import('next').NextConfig} */

const withPWA = require('next-pwa')({
    dest: 'public',
    register: true,
    skipWaiting: true,
    disable: process.env.NODE_ENV === 'development',
    // Production PWA optimizations
    runtimeCaching: [
        {
            urlPattern: /^https?.*/,
            handler: 'NetworkFirst',
            options: {
                cacheName: 'offlineCache',
                expiration: {
                    maxEntries: 200,
                    maxAgeSeconds: 24 * 60 * 60 // 24 hours
                }
            }
        }
    ]
});

const withBundleAnalyzer = require('@next/bundle-analyzer')({
    enabled: process.env.BUNDLE_ANALYZER === 'true'
});

const nextConfig = {
    // Performance optimizations
    reactStrictMode: true,
    swcMinify: true,

    // Experimental features for better performance
    experimental: {
        serverActions: true,
        optimizeCss: true,
        optimizePackageImports: ['primereact', 'primeicons', 'chart.js'],
        turbo: {
            rules: {
                '*.svg': {
                    loaders: ['@svgr/webpack'],
                    as: '*.js'
                }
            }
        }
    },

    // Compression and optimization
    compress: true,

    // Image optimization
    images: {
        formats: ['image/webp', 'image/avif'],
        minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
        dangerouslyAllowSVG: true,
        contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
        deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
        imageSizes: [16, 32, 48, 64, 96, 128, 256, 384]
    },

    // Headers for security and performance
    async headers() {
        return [
            {
                source: '/(.*)',
                headers: [
                    {
                        key: 'X-Frame-Options',
                        value: 'DENY'
                    },
                    {
                        key: 'X-Content-Type-Options',
                        value: 'nosniff'
                    },
                    {
                        key: 'Referrer-Policy',
                        value: 'strict-origin-when-cross-origin'
                    },
                    {
                        key: 'Permissions-Policy',
                        value: 'camera=(), microphone=(), geolocation=()'
                    }
                ]
            },
            {
                source: '/api/(.*)',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'no-store, max-age=0'
                    }
                ]
            },
            {
                source: '/_next/static/(.*)',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'public, max-age=31536000, immutable'
                    }
                ]
            }
        ];
    },

    // Redirects
    async redirects() {
        return [
            {
                source: '/',
                destination: '/auth',
                permanent: true
            }
        ];
    },

    // Webpack optimizations
    webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
        // Optimize bundle size
        config.optimization = {
            ...config.optimization,
            splitChunks: {
                chunks: 'all',
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                        priority: 10
                    },
                    primereact: {
                        test: /[\\/]node_modules[\\/](primereact|primeicons)[\\/]/,
                        name: 'primereact',
                        chunks: 'all',
                        priority: 20
                    },
                    common: {
                        name: 'common',
                        minChunks: 2,
                        chunks: 'all',
                        priority: 5,
                        reuseExistingChunk: true
                    }
                }
            }
        };

        // Canvas externalization for server-side
        config.externals = [...(config.externals || []), { canvas: 'canvas' }];

        // Ignore source maps in production for smaller bundles
        if (!dev) {
            config.devtool = false;
        }

        return config;
    },

    // Output configuration
    output: 'standalone',

    // Environment variables to expose to the client
    env: {
        CUSTOM_KEY: process.env.CUSTOM_KEY,
    },

    // TypeScript configuration
    typescript: {
        ignoreBuildErrors: false,
    },

    // ESLint configuration
    eslint: {
        ignoreDuringBuilds: false,
    },

    // Trailing slash configuration
    trailingSlash: false,

    // PoweredByHeader
    poweredByHeader: false,
};

module.exports = withBundleAnalyzer(withPWA(nextConfig));
