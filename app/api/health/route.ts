import { NextRequest, NextResponse } from 'next/server';
import { checkDatabaseHealth } from '@/lib/mongo';
import { checkRedisHealth } from '@/lib/redis';
import { env } from '@/lib/env-validation';

export async function GET(request: NextRequest) {
    const startTime = Date.now();
    
    try {
        // Check database health
        const dbHealthy = await checkDatabaseHealth();
        
        // Check Redis health
        const redisHealthy = await checkRedisHealth();
        
        // Calculate response time
        const responseTime = Date.now() - startTime;
        
        // Determine overall health
        const healthy = dbHealthy && redisHealthy;
        
        const healthData = {
            status: healthy ? 'healthy' : 'unhealthy',
            timestamp: new Date().toISOString(),
            responseTime: `${responseTime}ms`,
            version: '1.0.0',
            environment: env.NODE_ENV,
            services: {
                database: {
                    status: dbHealthy ? 'healthy' : 'unhealthy',
                    type: 'MongoDB'
                },
                cache: {
                    status: redisHealthy ? 'healthy' : 'unhealthy',
                    type: 'Redis'
                }
            },
            uptime: process.uptime(),
            memory: {
                used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
                total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
                external: Math.round(process.memoryUsage().external / 1024 / 1024)
            }
        };
        
        return NextResponse.json(
            healthData,
            { 
                status: healthy ? 200 : 503,
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            }
        );
    } catch (error) {
        console.error('Health check failed:', error);
        
        return NextResponse.json(
            {
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                error: 'Health check failed',
                responseTime: `${Date.now() - startTime}ms`
            },
            { 
                status: 503,
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            }
        );
    }
}

// Readiness probe - checks if the application is ready to serve traffic
export async function HEAD(request: NextRequest) {
    try {
        const dbHealthy = await checkDatabaseHealth();
        const redisHealthy = await checkRedisHealth();
        
        if (dbHealthy && redisHealthy) {
            return new NextResponse(null, { status: 200 });
        } else {
            return new NextResponse(null, { status: 503 });
        }
    } catch {
        return new NextResponse(null, { status: 503 });
    }
}
