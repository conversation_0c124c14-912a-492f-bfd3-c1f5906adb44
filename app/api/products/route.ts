import { NextRequest, NextResponse } from 'next/server';
import { BaseRepository } from '@/lib/database-optimization';
import { CACHE_CONFIG, defaultCache } from '@/lib/cache-strategies';
import { withPerformanceLogging } from '@/lib/performance';
import connectDB from '@/lib/mongo';
import mongoose from 'mongoose';

// Product schema (example)
const productSchema = new mongoose.Schema({
    name: { type: String, required: true, index: true },
    description: { type: String },
    price: { type: Number, required: true, index: true },
    category: { type: String, required: true, index: true },
    stock: { type: Number, default: 0 },
    sku: { type: String, unique: true, required: true },
    isActive: { type: Boolean, default: true, index: true },
    createdAt: { type: Date, default: Date.now, index: true },
    updatedAt: { type: Date, default: Date.now }
});

// Add compound indexes for common queries
productSchema.index({ category: 1, isActive: 1 });
productSchema.index({ price: 1, isActive: 1 });
productSchema.index({ name: 'text', description: 'text' });

const Product = mongoose.models.Product || mongoose.model('Product', productSchema);

// Product repository with caching
class ProductRepository extends BaseRepository<any> {
    constructor() {
        super(Product, 'product');
        this.defaultCacheTTL = CACHE_CONFIG.TTL.MEDIUM;
    }

    // Get products by category with caching
    async getByCategory(category: string, page: number = 1, limit: number = 10) {
        const cacheKey = this.getCacheKey('getByCategory', { category, page, limit });
        
        return defaultCache.get(cacheKey) || await defaultCache.set(
            cacheKey,
            await this.findPaginated(
                { category, isActive: true },
                page,
                limit,
                { createdAt: -1 }
            ),
            CACHE_CONFIG.TTL.MEDIUM
        );
    }

    // Get featured products
    async getFeaturedProducts(limit: number = 10) {
        const cacheKey = this.getCacheKey('getFeatured', { limit });
        
        const cached = await defaultCache.get(cacheKey);
        if (cached) return cached;

        const products = await this.model
            .find({ isActive: true, featured: true })
            .sort({ createdAt: -1 })
            .limit(limit)
            .lean()
            .exec();

        await defaultCache.set(cacheKey, products, CACHE_CONFIG.TTL.LONG);
        return products;
    }

    // Search products with caching
    async searchProducts(query: string, filters: any = {}, page: number = 1, limit: number = 10) {
        const cacheKey = this.getCacheKey('search', { query, filters, page, limit });
        
        const cached = await defaultCache.get(cacheKey);
        if (cached) return cached;

        const searchFilter = {
            ...filters,
            isActive: true,
            $text: { $search: query }
        };

        const result = await this.findPaginated(
            searchFilter,
            page,
            limit,
            { score: { $meta: 'textScore' } }
        );

        await defaultCache.set(cacheKey, result, CACHE_CONFIG.TTL.SHORT);
        return result;
    }
}

const productRepo = new ProductRepository();

// GET /api/products
export async function GET(request: NextRequest) {
    try {
        await connectDB();

        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 100); // Max 100 items
        const category = searchParams.get('category');
        const search = searchParams.get('search');
        const sortBy = searchParams.get('sortBy') || 'createdAt';
        const sortOrder = searchParams.get('sortOrder') === 'asc' ? 1 : -1;

        let result;

        if (search) {
            // Search products
            result = await withPerformanceLogging(
                () => productRepo.searchProducts(search, { category }, page, limit),
                'products.search'
            )();
        } else if (category) {
            // Get products by category
            result = await withPerformanceLogging(
                () => productRepo.getByCategory(category, page, limit),
                'products.getByCategory'
            )();
        } else {
            // Get all products with pagination
            const filter = { isActive: true };
            const sort = { [sortBy]: sortOrder };
            
            result = await withPerformanceLogging(
                () => productRepo.findPaginated(filter, page, limit, sort),
                'products.findPaginated'
            )();
        }

        return NextResponse.json({
            success: true,
            data: result.data,
            pagination: {
                page: result.page,
                limit,
                total: result.total,
                totalPages: result.totalPages,
                hasNext: result.page < result.totalPages,
                hasPrev: result.page > 1
            }
        }, {
            headers: {
                'Cache-Control': 'public, max-age=300, stale-while-revalidate=600',
                'X-Total-Count': result.total.toString()
            }
        });

    } catch (error) {
        console.error('Products API error:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to fetch products'
        }, { status: 500 });
    }
}

// POST /api/products
export async function POST(request: NextRequest) {
    try {
        await connectDB();

        const body = await request.json();
        
        // Validate required fields
        const { name, price, category, sku } = body;
        if (!name || !price || !category || !sku) {
            return NextResponse.json({
                success: false,
                error: 'Missing required fields: name, price, category, sku'
            }, { status: 400 });
        }

        const productData = {
            ...body,
            updatedAt: new Date()
        };

        const product = await withPerformanceLogging(
            () => productRepo.create(productData),
            'products.create'
        )();

        // Invalidate related caches
        await Promise.all([
            defaultCache.invalidate(`product:list:*`),
            defaultCache.invalidate(`product:getByCategory:*`),
            defaultCache.invalidate(`product:getFeatured:*`)
        ]);

        return NextResponse.json({
            success: true,
            data: product
        }, { status: 201 });

    } catch (error) {
        console.error('Product creation error:', error);
        
        if (error.code === 11000) {
            return NextResponse.json({
                success: false,
                error: 'Product with this SKU already exists'
            }, { status: 409 });
        }

        return NextResponse.json({
            success: false,
            error: 'Failed to create product'
        }, { status: 500 });
    }
}

// PUT /api/products (bulk update)
export async function PUT(request: NextRequest) {
    try {
        await connectDB();

        const body = await request.json();
        const { ids, updates } = body;

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return NextResponse.json({
                success: false,
                error: 'Invalid or missing product IDs'
            }, { status: 400 });
        }

        const updateData = {
            ...updates,
            updatedAt: new Date()
        };

        const result = await withPerformanceLogging(
            () => Product.updateMany(
                { _id: { $in: ids } },
                { $set: updateData }
            ),
            'products.bulkUpdate'
        )();

        // Invalidate caches
        await Promise.all([
            defaultCache.invalidate(`product:*`),
            ...ids.map(id => defaultCache.invalidate(`product:findById:${id}`))
        ]);

        return NextResponse.json({
            success: true,
            data: {
                modifiedCount: result.modifiedCount,
                matchedCount: result.matchedCount
            }
        });

    } catch (error) {
        console.error('Bulk update error:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to update products'
        }, { status: 500 });
    }
}
