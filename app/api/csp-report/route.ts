import { NextRequest, NextResponse } from 'next/server';
import { env } from '@/lib/env-validation';

interface CSPReport {
    'csp-report': {
        'document-uri': string;
        referrer: string;
        'violated-directive': string;
        'effective-directive': string;
        'original-policy': string;
        disposition: string;
        'blocked-uri': string;
        'line-number': number;
        'column-number': number;
        'source-file': string;
        'status-code': number;
        'script-sample': string;
    };
}

export async function POST(request: NextRequest) {
    try {
        // Only accept CSP reports in production
        if (env.NODE_ENV !== 'production') {
            return NextResponse.json({ message: 'CSP reporting disabled in development' }, { status: 200 });
        }

        const report: CSPReport = await request.json();
        const cspReport = report['csp-report'];

        if (!cspReport) {
            return NextResponse.json({ error: 'Invalid CSP report format' }, { status: 400 });
        }

        // Log CSP violation
        console.warn('CSP Violation Report:', {
            timestamp: new Date().toISOString(),
            documentUri: cspReport['document-uri'],
            violatedDirective: cspReport['violated-directive'],
            blockedUri: cspReport['blocked-uri'],
            sourceFile: cspReport['source-file'],
            lineNumber: cspReport['line-number'],
            columnNumber: cspReport['column-number'],
            userAgent: request.headers.get('user-agent'),
            ip: request.ip || request.headers.get('x-forwarded-for')
        });

        // In a production environment, you might want to:
        // 1. Store reports in a database
        // 2. Send alerts for critical violations
        // 3. Aggregate reports for analysis
        // 4. Send to external monitoring services (e.g., Sentry)

        // Example: Send to external monitoring service
        if (env.SENTRY_DSN) {
            // You could send this to Sentry or another monitoring service
            // await sendToMonitoringService(cspReport);
        }

        return NextResponse.json({ message: 'CSP report received' }, { status: 200 });

    } catch (error) {
        console.error('Error processing CSP report:', error);
        return NextResponse.json({ error: 'Failed to process CSP report' }, { status: 500 });
    }
}

// Handle GET requests (should not be used for CSP reports)
export async function GET() {
    return NextResponse.json({ 
        message: 'CSP report endpoint - POST only',
        documentation: 'This endpoint receives Content Security Policy violation reports'
    }, { status: 405 });
}
